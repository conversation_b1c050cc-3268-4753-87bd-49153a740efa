// اختبار الدوال مباشرة
const BugBountyCore = require('./assets/modules/bugbounty/BugBountyCore.js');

async function testFunctionsDirectly() {
    console.log('🔧 اختبار الدوال مباشرة...');
    
    try {
        // إنشاء instance من النظام
        const bugBountyCore = new BugBountyCore();
        
        // إنشاء ثغرة تجريبية
        const testVulnerability = {
            name: 'SQL Injection',
            type: 'SQL Injection',
            severity: 'High',
            url: 'https://testphp.vulnweb.com/login.php',
            location: 'https://testphp.vulnweb.com/login.php',
            target_url: 'https://testphp.vulnweb.com/login.php',
            description: 'SQL injection vulnerability in login form',
            payload: "admin' OR '1'='1' --"
        };
        
        console.log('📝 اختبار دوال الإصلاحات...');
        
        // اختبار دالة getCorrectFolderName
        if (typeof bugBountyCore.getCorrectFolderName === 'function') {
            const folderName = bugBountyCore.getCorrectFolderName(testVulnerability);
            console.log(`✅ اسم المجلد: ${folderName}`);
            
            // اختبار دالة getCleanVulnerabilityName
            if (typeof bugBountyCore.getCleanVulnerabilityName === 'function') {
                const cleanVulnName = bugBountyCore.getCleanVulnerabilityName(testVulnerability);
                console.log(`✅ اسم الثغرة المنظف: ${cleanVulnName}`);
                
                // اختبار دالة getCorrectImageName
                if (typeof bugBountyCore.getCorrectImageName === 'function') {
                    const beforeImage = bugBountyCore.getCorrectImageName('before', cleanVulnName, folderName);
                    const duringImage = bugBountyCore.getCorrectImageName('during', cleanVulnName, folderName);
                    const afterImage = bugBountyCore.getCorrectImageName('after', cleanVulnName, folderName);
                    
                    console.log(`✅ صورة قبل: ${beforeImage}`);
                    console.log(`✅ صورة أثناء: ${duringImage}`);
                    console.log(`✅ صورة بعد: ${afterImage}`);
                    
                    // تعيين analysisState
                    bugBountyCore.analysisState = {
                        reportId: folderName,
                        currentScanFolder: folderName,
                        vulnerabilities: [testVulnerability]
                    };
                    
                    console.log(`✅ Report ID: ${bugBountyCore.analysisState.reportId}`);
                    
                    // اختبار generateReportId المحدث
                    if (typeof bugBountyCore.generateReportId === 'function') {
                        const newReportId = bugBountyCore.generateReportId();
                        console.log(`✅ Report ID الجديد: ${newReportId}`);
                    } else {
                        console.log('❌ دالة generateReportId غير موجودة');
                    }
                    
                    // التحقق من النتائج
                    console.log('\n📊 ملخص النتائج:');
                    console.log(`📁 اسم المجلد: ${folderName}`);
                    console.log(`🏷️ اسم الثغرة: ${cleanVulnName}`);
                    console.log(`📸 أسماء الصور:`);
                    console.log(`  - ${beforeImage}`);
                    console.log(`  - ${duringImage}`);
                    console.log(`  - ${afterImage}`);
                    
                    // التحقق من صحة النمط
                    const isCorrectPattern = beforeImage.includes(cleanVulnName) && 
                                            beforeImage.includes(folderName) && 
                                            beforeImage.startsWith('before_');
                    
                    console.log(`\n🎯 النمط صحيح: ${isCorrectPattern ? '✅' : '❌'}`);
                    
                    if (isCorrectPattern) {
                        console.log('🎉 جميع الإصلاحات تعمل بشكل صحيح!');
                        
                        // اختبار إضافي: التحقق من أن الأسماء لا تحتوي على "report_"
                        const noReportPrefix = !folderName.includes('report_') && 
                                              !beforeImage.includes('report_') &&
                                              !duringImage.includes('report_') &&
                                              !afterImage.includes('report_');
                        
                        console.log(`🚫 لا يحتوي على "report_": ${noReportPrefix ? '✅' : '❌'}`);
                        
                        // اختبار إضافي: التحقق من أن الأسماء تحتوي على اسم الموقع
                        const containsSiteName = folderName.includes('testphp_vulnweb_com') &&
                                               beforeImage.includes('testphp_vulnweb_com') &&
                                               duringImage.includes('testphp_vulnweb_com') &&
                                               afterImage.includes('testphp_vulnweb_com');
                        
                        console.log(`🌐 يحتوي على اسم الموقع: ${containsSiteName ? '✅' : '❌'}`);
                        
                        if (noReportPrefix && containsSiteName) {
                            console.log('\n🎉 جميع الاختبارات نجحت! الإصلاحات مطبقة بالكامل!');
                        } else {
                            console.log('\n⚠️ بعض الاختبارات فشلت - يحتاج مراجعة');
                        }
                        
                    } else {
                        console.log('❌ هناك مشكلة في الإصلاحات');
                    }
                    
                } else {
                    console.log('❌ دالة getCorrectImageName غير موجودة');
                }
            } else {
                console.log('❌ دالة getCleanVulnerabilityName غير موجودة');
            }
        } else {
            console.log('❌ دالة getCorrectFolderName غير موجودة');
        }
        
    } catch (error) {
        console.error('❌ خطأ في الاختبار:', error);
    }
}

// تشغيل الاختبار
testFunctionsDirectly().then(() => {
    console.log('\n✅ انتهى الاختبار');
}).catch(error => {
    console.error('❌ فشل الاختبار:', error);
});
