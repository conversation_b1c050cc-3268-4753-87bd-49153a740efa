<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار فعلي كامل للنظام v4.0</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
            direction: rtl;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 10px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 2px solid #17a2b8;
            border-radius: 10px;
            background: #f8f9fa;
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }
        .btn:hover { background: #0056b3; }
        .btn:disabled { background: #6c757d; cursor: not-allowed; }
        .btn-success { background: #28a745; }
        .btn-danger { background: #dc3545; }
        .btn-warning { background: #ffc107; color: #212529; }
        
        .progress {
            width: 100%;
            height: 25px;
            background: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        .progress-bar {
            height: 100%;
            background: linear-gradient(90deg, #28a745, #20c997);
            width: 0%;
            transition: width 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
        }
        
        #testResults {
            max-height: 500px;
            overflow-y: auto;
            border: 1px solid #ddd;
            padding: 15px;
            background: #fff;
            border-radius: 5px;
            margin: 10px 0;
        }
        
        .result-item {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            border-left: 4px solid #007bff;
        }
        .result-success { background: #d4edda; border-left-color: #28a745; }
        .result-failure { background: #f8d7da; border-left-color: #dc3545; }
        .result-info { background: #d1ecf1; border-left-color: #17a2b8; }
        
        .screenshot-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .screenshot-item {
            border: 2px solid #ddd;
            border-radius: 10px;
            padding: 15px;
            background: white;
            text-align: center;
        }
        .screenshot-item img {
            max-width: 100%;
            height: 200px;
            object-fit: contain;
            border-radius: 5px;
            border: 1px solid #ddd;
        }
        
        .report-preview {
            max-height: 400px;
            overflow-y: auto;
            border: 2px solid #28a745;
            border-radius: 10px;
            padding: 20px;
            background: #f8f9fa;
            margin: 20px 0;
        }
        
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #3498db;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-left: 8px;
        }
        .status-success { background: #28a745; }
        .status-failure { background: #dc3545; }
        .status-pending { background: #ffc107; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧪 اختبار فعلي كامل للنظام v4.0</h1>
            <p>اختبار شامل مع تشغيل فعلي للثغرات والصور والتقارير</p>
        </div>

        <div class="test-section">
            <h2>🎯 إعدادات الاختبار الفعلي</h2>
            <div>
                <label>🌐 الموقع المستهدف:</label>
                <input type="text" id="testUrl" value="http://testphp.vulnweb.com/" style="width: 300px; padding: 8px; margin: 5px;">
            </div>
            <div>
                <button class="btn btn-success" onclick="startFullSystemTest()" id="startBtn">
                    🚀 بدء الاختبار الفعلي الكامل
                </button>
                <button class="btn btn-warning" onclick="testScreenshotsOnly()" id="screenshotBtn">
                    📸 اختبار الصور فقط
                </button>
                <button class="btn btn-warning" onclick="testReportsOnly()" id="reportBtn">
                    📋 اختبار التقارير فقط
                </button>
                <button class="btn" onclick="clearAllResults()" id="clearBtn">
                    🗑️ مسح جميع النتائج
                </button>
            </div>
            <div class="progress">
                <div class="progress-bar" id="progressBar">0%</div>
            </div>
            <div id="currentStatus" class="result-info" style="display: none;">
                <span class="loading"></span> <span id="statusText">جاري التحضير...</span>
            </div>
        </div>

        <div class="test-section">
            <h2>📊 نتائج الاختبار الفعلي</h2>
            <div id="testResults">
                <div class="result-info">انقر على "بدء الاختبار الفعلي الكامل" لبدء الاختبار الشامل</div>
            </div>
        </div>

        <div class="test-section">
            <h2>📸 الصور المُلتقطة</h2>
            <div id="screenshotResults">
                <div class="result-info">ستظهر الصور المُلتقطة هنا بعد تشغيل الاختبار</div>
            </div>
        </div>

        <div class="test-section">
            <h2>📋 معاينة التقرير المُنتج</h2>
            <div id="reportPreview">
                <div class="result-info">سيظهر التقرير المُنتج هنا بعد تشغيل الاختبار</div>
            </div>
        </div>

        <div class="test-section">
            <h2>📈 إحصائيات مفصلة</h2>
            <div id="detailedStats">
                <div class="result-info">ستظهر الإحصائيات المفصلة هنا بعد تشغيل الاختبار</div>
            </div>
        </div>
    </div>

    <!-- تحميل ملفات النظام v4.0 -->
    <script src="./assets/modules/bugbounty/BugBountyCore.js"></script>
    <script src="./assets/modules/bugbounty/impact_visualizer.js"></script>
    <script src="./assets/modules/bugbounty/python_screenshot_bridge.js"></script>
    <script src="./assets/modules/bugbounty/report_exporter.js"></script>

    <script>
        let testInProgress = false;
        let testResults = [];
        let discoveredVulnerabilities = [];
        let testedVulnerabilities = [];
        let capturedScreenshots = {};
        let generatedReport = null;

        async function startFullSystemTest() {
            if (testInProgress) {
                alert('الاختبار قيد التنفيذ بالفعل');
                return;
            }

            testInProgress = true;
            testResults = [];
            discoveredVulnerabilities = [];
            testedVulnerabilities = [];
            capturedScreenshots = {};
            generatedReport = null;
            
            const testUrl = document.getElementById('testUrl').value;
            const startBtn = document.getElementById('startBtn');
            
            startBtn.disabled = true;
            startBtn.textContent = '⏳ جاري التشغيل الفعلي...';
            
            updateProgress(0, 'بدء الاختبار الفعلي الكامل');
            showStatus('تهيئة النظام للاختبار الفعلي...');
            
            try {
                // المرحلة 1: اكتشاف الثغرات تلقائياً
                await runRealTest('🔍 اكتشاف الثغرات تلقائياً', 15, async () => {
                    return await realVulnerabilityDiscovery(testUrl);
                });
                
                // المرحلة 2: اختبار الثغرات فعلياً
                await runRealTest('⚡ اختبار الثغرات فعلياً', 30, async () => {
                    return await realVulnerabilityTesting(testUrl);
                });
                
                // المرحلة 3: التقاط الصور الحقيقية
                await runRealTest('📸 التقاط الصور الحقيقية', 50, async () => {
                    return await realScreenshotCapture(testUrl);
                });
                
                // المرحلة 4: إنتاج التقرير الفعلي
                await runRealTest('📋 إنتاج التقرير الفعلي', 75, async () => {
                    return await realReportGeneration(testUrl);
                });
                
                // المرحلة 5: التحقق النهائي
                await runRealTest('✅ التحقق النهائي', 100, async () => {
                    return await finalVerification();
                });
                
                hideStatus();
                displayFinalResults();
                
            } catch (error) {
                addTestResult('❌ خطأ في الاختبار الفعلي', false, `خطأ عام: ${error.message}`);
                console.error('خطأ في الاختبار:', error);
            } finally {
                testInProgress = false;
                startBtn.disabled = false;
                startBtn.textContent = '🚀 بدء الاختبار الفعلي الكامل';
                updateProgress(100, 'انتهى الاختبار');
            }
        }

        async function runRealTest(testName, progressPercent, testFunction) {
            showStatus(`جاري تنفيذ: ${testName}`);
            updateProgress(progressPercent, testName);
            
            try {
                const result = await testFunction();
                addTestResult(testName, result.success, result.details);
                
                if (result.data) {
                    // حفظ البيانات للمراحل التالية
                    if (result.data.vulnerabilities) {
                        discoveredVulnerabilities = result.data.vulnerabilities;
                    }
                    if (result.data.testedVulnerabilities) {
                        testedVulnerabilities = result.data.testedVulnerabilities;
                    }
                    if (result.data.screenshots) {
                        capturedScreenshots = result.data.screenshots;
                        displayScreenshots(result.data.screenshots);
                    }
                    if (result.data.report) {
                        generatedReport = result.data.report;
                        displayReport(result.data.report);
                    }
                }
                
                await sleep(1500); // توقف بين المراحل
                
            } catch (error) {
                addTestResult(testName, false, `خطأ: ${error.message}`);
                throw error;
            }
        }

        async function realVulnerabilityDiscovery(url) {
            showStatus('🔍 جاري اكتشاف الثغرات تلقائياً...');
            
            try {
                const core = new window.BugBountyCore();
                const scanResult = await core.performQuickScan(url);
                
                if (scanResult && scanResult.success && scanResult.vulnerabilities) {
                    const vulnCount = scanResult.vulnerabilities.length;
                    return {
                        success: true,
                        details: `✅ تم اكتشاف ${vulnCount} ثغرة تلقائياً: ${scanResult.vulnerabilities.map(v => v.name).join(', ')}`,
                        data: { vulnerabilities: scanResult.vulnerabilities }
                    };
                } else {
                    return {
                        success: false,
                        details: '❌ فشل في اكتشاف الثغرات تلقائياً'
                    };
                }
                
            } catch (error) {
                return {
                    success: false,
                    details: `❌ خطأ في اكتشاف الثغرات: ${error.message}`
                };
            }
        }

        async function realVulnerabilityTesting(url) {
            showStatus('⚡ جاري اختبار الثغرات فعلياً...');
            
            try {
                if (!discoveredVulnerabilities || discoveredVulnerabilities.length === 0) {
                    return {
                        success: false,
                        details: '❌ لا توجد ثغرات مكتشفة للاختبار'
                    };
                }

                const visualizer = new window.ImpactVisualizer();
                let tested = [];
                
                // اختبار كل ثغرة مكتشفة
                for (const vuln of discoveredVulnerabilities) {
                    showStatus(`⚡ جاري اختبار الثغرة: ${vuln.name}`);
                    
                    try {
                        const testResult = await visualizer.simulateSecureExploitation(vuln, { url: url });
                        
                        if (testResult && testResult.success) {
                            tested.push({
                                name: vuln.name,
                                severity: vuln.severity,
                                tested: true,
                                exploitable: true,
                                poc: testResult.poc,
                                screenshots: testResult.screenshots || {}
                            });
                        }
                    } catch (error) {
                        console.warn(`فشل في اختبار الثغرة ${vuln.name}:`, error);
                    }
                }
                
                const success = tested.length > 0;
                return {
                    success: success,
                    details: success ? 
                        `✅ تم اختبار ${tested.length} ثغرة بنجاح من أصل ${discoveredVulnerabilities.length}` :
                        '❌ فشل في اختبار أي ثغرة',
                    data: { testedVulnerabilities: tested }
                };
                
            } catch (error) {
                return {
                    success: false,
                    details: `❌ خطأ في اختبار الثغرات: ${error.message}`
                };
            }
        }

        async function realScreenshotCapture(url) {
            showStatus('📸 جاري التقاط الصور الحقيقية...');
            
            try {
                if (!testedVulnerabilities || testedVulnerabilities.length === 0) {
                    return {
                        success: false,
                        details: '❌ لا توجد ثغرات مُختبرة لالتقاط صورها'
                    };
                }

                const bridge = new window.PythonScreenshotBridge();
                let screenshots = {};
                let successCount = 0;
                
                // التقاط صور لكل ثغرة مُختبرة
                for (const vuln of testedVulnerabilities.slice(0, 2)) { // أول ثغرتين فقط للاختبار
                    showStatus(`📸 جاري التقاط صور للثغرة: ${vuln.name}`);
                    
                    try {
                        const reportId = `test_${Date.now()}`;
                        const screenshotResult = await bridge.captureVulnerabilitySequence(url, reportId, vuln.name);
                        
                        if (screenshotResult && screenshotResult.success) {
                            screenshots[vuln.name] = screenshotResult;
                            successCount++;
                        }
                    } catch (error) {
                        console.warn(`فشل في التقاط صور للثغرة ${vuln.name}:`, error);
                    }
                }
                
                const success = successCount > 0;
                return {
                    success: success,
                    details: success ? 
                        `✅ تم التقاط صور لـ ${successCount} ثغرة بنجاح` :
                        '❌ فشل في التقاط أي صور',
                    data: { screenshots: screenshots }
                };
                
            } catch (error) {
                return {
                    success: false,
                    details: `❌ خطأ في التقاط الصور: ${error.message}`
                };
            }
        }

        async function realReportGeneration(url) {
            showStatus('📋 جاري إنتاج التقرير الفعلي...');
            
            try {
                if (!testedVulnerabilities || testedVulnerabilities.length === 0) {
                    return {
                        success: false,
                        details: '❌ لا توجد ثغرات مُختبرة لإنتاج تقرير'
                    };
                }

                const core = new window.BugBountyCore();
                
                const reportData = {
                    url: url,
                    vulnerabilities: testedVulnerabilities,
                    screenshots: capturedScreenshots,
                    timestamp: new Date().toISOString(),
                    reportType: 'comprehensive_v4'
                };
                
                const reportResult = await core.generateComprehensiveReport(reportData);
                
                const success = reportResult && reportResult.length > 1000; // تقرير حقيقي يجب أن يكون أكبر من 1KB
                
                return {
                    success: success,
                    details: success ? 
                        `✅ تم إنتاج تقرير شامل (${Math.round(reportResult.length/1024)}KB) للثغرات المُختبرة` :
                        '❌ فشل في إنتاج التقرير أو التقرير فارغ',
                    data: { report: reportResult }
                };
                
            } catch (error) {
                return {
                    success: false,
                    details: `❌ خطأ في إنتاج التقرير: ${error.message}`
                };
            }
        }

        async function finalVerification() {
            showStatus('✅ جاري التحقق النهائي...');
            
            const hasVulnerabilities = discoveredVulnerabilities.length > 0;
            const hasTestedVulns = testedVulnerabilities.length > 0;
            const hasScreenshots = Object.keys(capturedScreenshots).length > 0;
            const hasReport = generatedReport && generatedReport.length > 1000;
            
            const successCount = [hasVulnerabilities, hasTestedVulns, hasScreenshots, hasReport].filter(Boolean).length;
            const totalChecks = 4;
            const successRate = Math.round((successCount / totalChecks) * 100);
            
            const success = successRate >= 75; // 75% نجاح مطلوب
            
            return {
                success: success,
                details: `📊 التحقق النهائي: ${successRate}% (${successCount}/${totalChecks}) - ${success ? '✅ النظام يعمل بشكل ممتاز' : '❌ النظام يحتاج تحسينات'}`
            };
        }

        function displayScreenshots(screenshots) {
            const container = document.getElementById('screenshotResults');
            let html = '<h3>📸 الصور المُلتقطة فعلياً:</h3>';
            
            if (Object.keys(screenshots).length === 0) {
                html += '<div class="result-info">لم يتم التقاط أي صور</div>';
            } else {
                html += '<div class="screenshot-container">';
                
                for (const [vulnName, screenshotData] of Object.entries(screenshots)) {
                    html += `
                        <div class="screenshot-item">
                            <h4>🔓 ${vulnName}</h4>
                            <div>
                                <strong>📷 قبل الاستغلال:</strong><br>
                                ${screenshotData.before ? 
                                    `<img src="data:image/png;base64,${screenshotData.before}" alt="قبل الاستغلال">` :
                                    '<div class="result-info">لا توجد صورة</div>'
                                }
                            </div>
                            <div>
                                <strong>⚡ أثناء الاستغلال:</strong><br>
                                ${screenshotData.during ? 
                                    `<img src="data:image/png;base64,${screenshotData.during}" alt="أثناء الاستغلال">` :
                                    '<div class="result-info">لا توجد صورة</div>'
                                }
                            </div>
                            <div>
                                <strong>🚨 بعد الاستغلال:</strong><br>
                                ${screenshotData.after ? 
                                    `<img src="data:image/png;base64,${screenshotData.after}" alt="بعد الاستغلال">` :
                                    '<div class="result-info">لا توجد صورة</div>'
                                }
                            </div>
                        </div>
                    `;
                }
                
                html += '</div>';
            }
            
            container.innerHTML = html;
        }

        function displayReport(reportContent) {
            const container = document.getElementById('reportPreview');
            
            if (!reportContent || reportContent.length < 100) {
                container.innerHTML = '<div class="result-failure">❌ لم يتم إنتاج تقرير صالح</div>';
                return;
            }
            
            container.innerHTML = `
                <h3>📋 معاينة التقرير المُنتج (${Math.round(reportContent.length/1024)}KB):</h3>
                <div class="report-preview">
                    ${reportContent}
                </div>
                <button class="btn btn-success" onclick="downloadReport()">📥 تحميل التقرير الكامل</button>
            `;
        }

        function downloadReport() {
            if (!generatedReport) {
                alert('لا يوجد تقرير للتحميل');
                return;
            }
            
            const blob = new Blob([generatedReport], { type: 'text/html' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `bug_bounty_report_${Date.now()}.html`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        }

        async function testScreenshotsOnly() {
            showStatus('📸 اختبار الصور فقط...');
            updateProgress(50, 'اختبار الصور');
            
            try {
                const url = document.getElementById('testUrl').value;
                const result = await realScreenshotCapture(url);
                addTestResult('📸 اختبار الصور فقط', result.success, result.details);
                
                if (result.data && result.data.screenshots) {
                    displayScreenshots(result.data.screenshots);
                }
            } catch (error) {
                addTestResult('📸 اختبار الصور فقط', false, `خطأ: ${error.message}`);
            } finally {
                hideStatus();
                updateProgress(100, 'انتهى اختبار الصور');
            }
        }

        async function testReportsOnly() {
            showStatus('📋 اختبار التقارير فقط...');
            updateProgress(50, 'اختبار التقارير');
            
            try {
                const url = document.getElementById('testUrl').value;
                
                // إنشاء بيانات تجريبية للاختبار
                testedVulnerabilities = [
                    { name: 'SQL Injection', severity: 'High', tested: true },
                    { name: 'XSS', severity: 'Medium', tested: true }
                ];
                
                const result = await realReportGeneration(url);
                addTestResult('📋 اختبار التقارير فقط', result.success, result.details);
                
                if (result.data && result.data.report) {
                    displayReport(result.data.report);
                }
            } catch (error) {
                addTestResult('📋 اختبار التقارير فقط', false, `خطأ: ${error.message}`);
            } finally {
                hideStatus();
                updateProgress(100, 'انتهى اختبار التقارير');
            }
        }

        function addTestResult(testName, success, details) {
            testResults.push({ name: testName, success: success, details: details, timestamp: new Date().toISOString() });
            
            const resultsDiv = document.getElementById('testResults');
            const resultClass = success ? 'result-success' : 'result-failure';
            const icon = success ? '✅' : '❌';
            const statusClass = success ? 'status-success' : 'status-failure';
            
            resultsDiv.innerHTML += `
                <div class="${resultClass}">
                    <span class="status-indicator ${statusClass}"></span>
                    ${icon} <strong>${testName}</strong><br>
                    📝 ${details}
                    <small style="display: block; margin-top: 5px; color: #666;">
                        ⏰ ${new Date().toLocaleTimeString()}
                    </small>
                </div>
            `;
            
            resultsDiv.scrollTop = resultsDiv.scrollHeight;
        }

        function displayFinalResults() {
            const passedTests = testResults.filter(r => r.success).length;
            const totalTests = testResults.length;
            const successRate = Math.round((passedTests / totalTests) * 100);
            
            const statsDiv = document.getElementById('detailedStats');
            statsDiv.innerHTML = `
                <h3>📈 إحصائيات مفصلة:</h3>
                <div class="result-info">
                    <strong>📊 معدل النجاح الإجمالي:</strong> ${successRate}% (${passedTests}/${totalTests})<br>
                    <strong>🔍 الثغرات المكتشفة:</strong> ${discoveredVulnerabilities.length}<br>
                    <strong>⚡ الثغرات المُختبرة:</strong> ${testedVulnerabilities.length}<br>
                    <strong>📸 مجموعات الصور:</strong> ${Object.keys(capturedScreenshots).length}<br>
                    <strong>📋 حجم التقرير:</strong> ${generatedReport ? Math.round(generatedReport.length/1024) + 'KB' : 'لا يوجد'}<br>
                    <strong>⏱️ وقت الاختبار:</strong> ${new Date().toLocaleTimeString()}
                </div>
            `;
        }

        function clearAllResults() {
            document.getElementById('testResults').innerHTML = '<div class="result-info">تم مسح جميع النتائج</div>';
            document.getElementById('screenshotResults').innerHTML = '<div class="result-info">ستظهر الصور المُلتقطة هنا بعد تشغيل الاختبار</div>';
            document.getElementById('reportPreview').innerHTML = '<div class="result-info">سيظهر التقرير المُنتج هنا بعد تشغيل الاختبار</div>';
            document.getElementById('detailedStats').innerHTML = '<div class="result-info">ستظهر الإحصائيات المفصلة هنا بعد تشغيل الاختبار</div>';
            updateProgress(0, 'تم مسح النتائج');
        }

        function updateProgress(percentage, text) {
            const progressBar = document.getElementById('progressBar');
            progressBar.style.width = percentage + '%';
            progressBar.textContent = `${Math.round(percentage)}% - ${text}`;
        }

        function showStatus(text) {
            document.getElementById('currentStatus').style.display = 'block';
            document.getElementById('statusText').textContent = text;
        }

        function hideStatus() {
            document.getElementById('currentStatus').style.display = 'none';
        }

        function sleep(ms) {
            return new Promise(resolve => setTimeout(resolve, ms));
        }

        // تهيئة الصفحة
        window.addEventListener('load', () => {
            console.log('🚀 تم تحميل اختبار النظام الفعلي الكامل v4.0');
        });
    </script>
</body>
</html>
