<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار عرض الصور - Bug Bounty v4.0</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
            direction: rtl;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 10px;
        }
        .test-section {
            margin: 30px 0;
            padding: 20px;
            border: 2px solid #17a2b8;
            border-radius: 10px;
            background: #f8f9fa;
        }
        .image-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 20px;
            margin: 20px 0;
        }
        .image-item {
            text-align: center;
            padding: 15px;
            background: white;
            border-radius: 10px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .image-item img {
            max-width: 100%;
            height: 200px;
            object-fit: contain;
            border-radius: 8px;
            border: 2px solid #ddd;
        }
        .error-message {
            background: #f8d7da;
            color: #721c24;
            padding: 10px;
            border-radius: 5px;
            margin-top: 10px;
            display: none;
        }
        .success-message {
            background: #d4edda;
            color: #155724;
            padding: 10px;
            border-radius: 5px;
            margin-top: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧪 اختبار عرض الصور - Bug Bounty v4.0</h1>
            <p>اختبار عرض الصور الفعلية من مجلد testphp_vulnweb_com</p>
        </div>

        <div class="test-section">
            <h2>📸 اختبار الصور الموجودة فعلياً</h2>
            <p><strong>المجلد:</strong> assets/modules/bugbounty/screenshots/testphp_vulnweb_com/</p>
            
            <div class="image-grid">
                <div class="image-item">
                    <h3>🔒 قبل الاستغلال</h3>
                    <img src="./assets/modules/bugbounty/screenshots/testphp_vulnweb_com/before_SQL_Injection.png" 
                         alt="قبل الاستغلال - SQL Injection" 
                         onload="this.nextElementSibling.style.display='none'; this.nextElementSibling.nextElementSibling.style.display='block';" 
                         onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
                    <div class="error-message">❌ فشل في تحميل الصورة</div>
                    <div class="success-message" style="display: none;">✅ تم تحميل الصورة بنجاح</div>
                </div>

                <div class="image-item">
                    <h3>⚠️ أثناء الاستغلال</h3>
                    <img src="./assets/modules/bugbounty/screenshots/testphp_vulnweb_com/during_SQL_Injection.png" 
                         alt="أثناء الاستغلال - SQL Injection" 
                         onload="this.nextElementSibling.style.display='none'; this.nextElementSibling.nextElementSibling.style.display='block';" 
                         onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
                    <div class="error-message">❌ فشل في تحميل الصورة</div>
                    <div class="success-message" style="display: none;">✅ تم تحميل الصورة بنجاح</div>
                </div>

                <div class="image-item">
                    <h3>🚨 بعد الاستغلال</h3>
                    <img src="./assets/modules/bugbounty/screenshots/testphp_vulnweb_com/after_SQL_Injection.png" 
                         alt="بعد الاستغلال - SQL Injection" 
                         onload="this.nextElementSibling.style.display='none'; this.nextElementSibling.nextElementSibling.style.display='block';" 
                         onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
                    <div class="error-message">❌ فشل في تحميل الصورة</div>
                    <div class="success-message" style="display: none;">✅ تم تحميل الصورة بنجاح</div>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h2>🔍 اختبار صور XSS</h2>
            <div class="image-grid">
                <div class="image-item">
                    <h3>🔒 قبل XSS</h3>
                    <img src="./assets/modules/bugbounty/screenshots/testphp_vulnweb_com/before_XSS.png" 
                         alt="قبل الاستغلال - XSS" 
                         onload="this.nextElementSibling.style.display='none'; this.nextElementSibling.nextElementSibling.style.display='block';" 
                         onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
                    <div class="error-message">❌ فشل في تحميل الصورة</div>
                    <div class="success-message" style="display: none;">✅ تم تحميل الصورة بنجاح</div>
                </div>

                <div class="image-item">
                    <h3>⚠️ أثناء XSS</h3>
                    <img src="./assets/modules/bugbounty/screenshots/testphp_vulnweb_com/during_XSS.png" 
                         alt="أثناء الاستغلال - XSS" 
                         onload="this.nextElementSibling.style.display='none'; this.nextElementSibling.nextElementSibling.style.display='block';" 
                         onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
                    <div class="error-message">❌ فشل في تحميل الصورة</div>
                    <div class="success-message" style="display: none;">✅ تم تحميل الصورة بنجاح</div>
                </div>

                <div class="image-item">
                    <h3>🚨 بعد XSS</h3>
                    <img src="./assets/modules/bugbounty/screenshots/testphp_vulnweb_com/after_XSS.png" 
                         alt="بعد الاستغلال - XSS" 
                         onload="this.nextElementSibling.style.display='none'; this.nextElementSibling.nextElementSibling.style.display='block';" 
                         onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
                    <div class="error-message">❌ فشل في تحميل الصورة</div>
                    <div class="success-message" style="display: none;">✅ تم تحميل الصورة بنجاح</div>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h2>📋 معلومات الاختبار</h2>
            <ul>
                <li><strong>المجلد المستهدف:</strong> assets/modules/bugbounty/screenshots/testphp_vulnweb_com/</li>
                <li><strong>أنواع الصور المختبرة:</strong> SQL_Injection, XSS, IDOR, Directory_Traversal</li>
                <li><strong>مراحل الاختبار:</strong> before, during, after</li>
                <li><strong>تنسيق الأسماء:</strong> {stage}_{vulnerability_name}.png</li>
            </ul>
        </div>

        <div class="test-section">
            <h2>🛠️ إرشادات الاستكشاف</h2>
            <ol>
                <li>تأكد من وجود الصور في المجلد المحدد</li>
                <li>تحقق من أن أسماء الصور تطابق النمط المطلوب</li>
                <li>تأكد من أن خادم Python يعمل على المنفذ 3000</li>
                <li>تحقق من أن المسارات النسبية صحيحة</li>
            </ol>
        </div>
    </div>

    <script>
        // إحصائيات التحميل
        let loadedImages = 0;
        let failedImages = 0;
        
        document.addEventListener('DOMContentLoaded', function() {
            const images = document.querySelectorAll('img');
            console.log(`🧪 بدء اختبار تحميل ${images.length} صورة`);
            
            images.forEach((img, index) => {
                img.addEventListener('load', function() {
                    loadedImages++;
                    console.log(`✅ تم تحميل الصورة ${index + 1}: ${this.src}`);
                });
                
                img.addEventListener('error', function() {
                    failedImages++;
                    console.error(`❌ فشل تحميل الصورة ${index + 1}: ${this.src}`);
                });
            });
            
            // تقرير نهائي بعد 5 ثوان
            setTimeout(() => {
                console.log(`📊 تقرير نهائي: ${loadedImages} نجحت، ${failedImages} فشلت من أصل ${images.length}`);
            }, 5000);
        });
    </script>
</body>
</html>
