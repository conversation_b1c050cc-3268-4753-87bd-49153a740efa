// اختبار فعلي للنظام v4.0 - التحقق من الثغرات المُختبرة تلقائياً والصور الحقيقية
console.log('🧪 بدء الاختبار الفعلي للنظام v4.0...');

class V4SystemTester {
    constructor() {
        this.testResults = [];
        this.testStartTime = Date.now();
        this.testUrl = 'http://testphp.vulnweb.com/';
        this.reportId = `test_${Date.now()}`;
    }

    async runCompleteTest() {
        console.log('🔍 بدء الاختبار الشامل للنظام v4.0...');
        
        try {
            // 1. اختبار تحميل النظام
            await this.testSystemLoading();
            
            // 2. اختبار الفحص التلقائي
            await this.testAutomaticScanning();
            
            // 3. اختبار اختبار الثغرات تلقائياً
            await this.testAutomaticVulnerabilityTesting();
            
            // 4. اختبار التقاط الصور الحقيقية
            await this.testRealScreenshotCapture();
            
            // 5. اختبار إنتاج التقارير
            await this.testReportGeneration();
            
            // عرض النتائج النهائية
            this.displayTestResults();
            
        } catch (error) {
            console.error('❌ خطأ في الاختبار:', error);
            this.addTestResult('System Test', false, `خطأ عام: ${error.message}`);
        }
    }

    async testSystemLoading() {
        console.log('📦 اختبار تحميل النظام v4.0...');
        
        try {
            // التحقق من وجود الكلاسات الأساسية
            const coreExists = typeof window.BugBountyCore !== 'undefined';
            const visualizerExists = typeof window.ImpactVisualizer !== 'undefined';
            const promptSystemExists = typeof window.PromptOnlyBugBountySystem !== 'undefined';
            
            this.addTestResult('Core System Loading', coreExists, 
                coreExists ? 'BugBountyCore محمل بنجاح' : 'BugBountyCore غير محمل');
            
            this.addTestResult('Impact Visualizer Loading', visualizerExists, 
                visualizerExists ? 'ImpactVisualizer محمل بنجاح' : 'ImpactVisualizer غير محمل');
            
            this.addTestResult('Prompt System Loading', promptSystemExists, 
                promptSystemExists ? 'PromptOnlyBugBountySystem محمل بنجاح' : 'PromptOnlyBugBountySystem غير محمل');
            
            return coreExists && visualizerExists && promptSystemExists;
            
        } catch (error) {
            this.addTestResult('System Loading', false, `خطأ في التحميل: ${error.message}`);
            return false;
        }
    }

    async testAutomaticScanning() {
        console.log('🔍 اختبار الفحص التلقائي...');
        
        try {
            if (typeof window.BugBountyCore === 'undefined') {
                this.addTestResult('Automatic Scanning', false, 'النظام الأساسي غير محمل');
                return false;
            }

            const core = new window.BugBountyCore();
            
            // بدء فحص تلقائي
            console.log(`🎯 بدء فحص تلقائي للموقع: ${this.testUrl}`);
            const scanResult = await core.startComprehensiveScan(this.testUrl);
            
            const scanSuccess = scanResult && scanResult.vulnerabilities && scanResult.vulnerabilities.length > 0;
            
            this.addTestResult('Automatic Vulnerability Scanning', scanSuccess, 
                scanSuccess ? `تم اكتشاف ${scanResult.vulnerabilities.length} ثغرة` : 'لم يتم اكتشاف ثغرات');
            
            if (scanSuccess) {
                console.log('✅ الثغرات المكتشفة:', scanResult.vulnerabilities.map(v => v.name));
                this.discoveredVulnerabilities = scanResult.vulnerabilities;
            }
            
            return scanSuccess;
            
        } catch (error) {
            this.addTestResult('Automatic Scanning', false, `خطأ في الفحص: ${error.message}`);
            return false;
        }
    }

    async testAutomaticVulnerabilityTesting() {
        console.log('⚡ اختبار اختبار الثغرات تلقائياً...');
        
        try {
            if (!this.discoveredVulnerabilities || this.discoveredVulnerabilities.length === 0) {
                this.addTestResult('Automatic Vulnerability Testing', false, 'لا توجد ثغرات مكتشفة للاختبار');
                return false;
            }

            const visualizer = new window.ImpactVisualizer();
            let testedVulnerabilities = [];
            
            // اختبار كل ثغرة مكتشفة تلقائياً
            for (const vuln of this.discoveredVulnerabilities.slice(0, 3)) { // اختبار أول 3 ثغرات فقط
                console.log(`🧪 اختبار الثغرة تلقائياً: ${vuln.name}`);
                
                const testResult = await visualizer.simulateSecureExploitation(vuln, { url: this.testUrl });
                
                if (testResult && testResult.success && testResult.poc && testResult.poc.success) {
                    testedVulnerabilities.push({
                        name: vuln.name,
                        tested: true,
                        exploitable: true,
                        poc: testResult.poc
                    });
                    console.log(`✅ تم اختبار واستغلال الثغرة بنجاح: ${vuln.name}`);
                } else {
                    console.log(`⚠️ فشل في اختبار الثغرة: ${vuln.name}`);
                }
            }
            
            const testingSuccess = testedVulnerabilities.length > 0;
            this.testedVulnerabilities = testedVulnerabilities;
            
            this.addTestResult('Automatic Vulnerability Testing', testingSuccess, 
                testingSuccess ? `تم اختبار ${testedVulnerabilities.length} ثغرة بنجاح` : 'فشل في اختبار الثغرات');
            
            return testingSuccess;
            
        } catch (error) {
            this.addTestResult('Automatic Vulnerability Testing', false, `خطأ في الاختبار: ${error.message}`);
            return false;
        }
    }

    async testRealScreenshotCapture() {
        console.log('📸 اختبار التقاط الصور الحقيقية...');
        
        try {
            if (!this.testedVulnerabilities || this.testedVulnerabilities.length === 0) {
                this.addTestResult('Real Screenshot Capture', false, 'لا توجد ثغرات مُختبرة لالتقاط صورها');
                return false;
            }

            // اختبار التقاط صور للثغرة الأولى المُختبرة
            const firstTestedVuln = this.testedVulnerabilities[0];
            console.log(`📷 اختبار التقاط صور للثغرة المُختبرة: ${firstTestedVuln.name}`);
            
            // محاولة استخدام Python screenshot service
            if (typeof window.PythonScreenshotBridge !== 'undefined') {
                const bridge = new window.PythonScreenshotBridge();
                
                const screenshotResult = await bridge.captureVulnerabilitySequence(
                    this.testUrl, 
                    this.reportId, 
                    firstTestedVuln.name
                );
                
                const screenshotSuccess = screenshotResult && screenshotResult.success;
                
                this.addTestResult('Real Screenshot Capture', screenshotSuccess, 
                    screenshotSuccess ? 'تم التقاط الصور الحقيقية بنجاح' : 'فشل في التقاط الصور');
                
                if (screenshotSuccess) {
                    console.log('✅ تم التقاط الصور:', screenshotResult);
                    this.capturedScreenshots = screenshotResult;
                }
                
                return screenshotSuccess;
            } else {
                this.addTestResult('Real Screenshot Capture', false, 'Python Screenshot Bridge غير متاح');
                return false;
            }
            
        } catch (error) {
            this.addTestResult('Real Screenshot Capture', false, `خطأ في التقاط الصور: ${error.message}`);
            return false;
        }
    }

    async testReportGeneration() {
        console.log('📋 اختبار إنتاج التقارير...');
        
        try {
            if (!this.testedVulnerabilities || this.testedVulnerabilities.length === 0) {
                this.addTestResult('Report Generation', false, 'لا توجد ثغرات مُختبرة لإنتاج تقرير');
                return false;
            }

            const core = new window.BugBountyCore();
            
            // إنتاج تقرير للثغرات المُختبرة
            const reportResult = await core.generateComprehensiveReport(this.testedVulnerabilities, {
                url: this.testUrl,
                reportId: this.reportId,
                screenshots: this.capturedScreenshots
            });
            
            const reportSuccess = reportResult && reportResult.success;
            
            this.addTestResult('Report Generation', reportSuccess, 
                reportSuccess ? 'تم إنتاج التقرير بنجاح' : 'فشل في إنتاج التقرير');
            
            if (reportSuccess) {
                console.log('✅ تم إنتاج التقرير:', reportResult);
                this.generatedReport = reportResult;
            }
            
            return reportSuccess;
            
        } catch (error) {
            this.addTestResult('Report Generation', false, `خطأ في إنتاج التقرير: ${error.message}`);
            return false;
        }
    }

    addTestResult(testName, success, details) {
        this.testResults.push({
            name: testName,
            success: success,
            details: details,
            timestamp: new Date().toISOString()
        });
        
        const status = success ? '✅' : '❌';
        console.log(`${status} ${testName}: ${details}`);
    }

    displayTestResults() {
        const testDuration = Date.now() - this.testStartTime;
        const passedTests = this.testResults.filter(r => r.success).length;
        const totalTests = this.testResults.length;
        
        console.log('\n' + '='.repeat(60));
        console.log('📊 نتائج الاختبار الفعلي للنظام v4.0');
        console.log('='.repeat(60));
        console.log(`⏱️ مدة الاختبار: ${testDuration}ms`);
        console.log(`📈 النتيجة: ${passedTests}/${totalTests} اختبار نجح`);
        console.log(`🎯 معدل النجاح: ${Math.round((passedTests/totalTests)*100)}%`);
        console.log('\n📋 تفاصيل الاختبارات:');
        
        this.testResults.forEach((result, index) => {
            const status = result.success ? '✅' : '❌';
            console.log(`${index + 1}. ${status} ${result.name}`);
            console.log(`   📝 ${result.details}`);
        });
        
        console.log('\n' + '='.repeat(60));
        
        if (passedTests === totalTests) {
            console.log('🎉 جميع الاختبارات نجحت! النظام v4.0 يعمل بشكل صحيح');
        } else {
            console.log('⚠️ بعض الاختبارات فشلت. يحتاج النظام إلى إصلاحات إضافية');
        }
    }
}

// تشغيل الاختبار الفعلي
async function runRealSystemTest() {
    const tester = new V4SystemTester();
    await tester.runCompleteTest();
    return tester;
}

// تشغيل الاختبار تلقائياً
if (typeof window !== 'undefined') {
    console.log('🚀 بدء الاختبار الفعلي للنظام v4.0...');
    runRealSystemTest().then(tester => {
        console.log('✅ انتهى الاختبار الفعلي');
        window.lastTestResults = tester.testResults;
    }).catch(error => {
        console.error('❌ فشل الاختبار الفعلي:', error);
    });
} else {
    // للتشغيل في Node.js
    module.exports = { V4SystemTester, runRealSystemTest };
}
