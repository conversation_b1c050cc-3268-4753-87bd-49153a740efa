<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار فعلي للنظام v4.0</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
            direction: rtl;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 10px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 2px solid #17a2b8;
            border-radius: 10px;
            background: #f8f9fa;
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        .test-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .test-failure {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .test-info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .btn:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }
        .progress {
            width: 100%;
            height: 20px;
            background: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        .progress-bar {
            height: 100%;
            background: linear-gradient(90deg, #28a745, #20c997);
            width: 0%;
            transition: width 0.3s ease;
        }
        #testResults {
            max-height: 400px;
            overflow-y: auto;
            border: 1px solid #ddd;
            padding: 15px;
            background: #fff;
            border-radius: 5px;
        }
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #3498db;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧪 اختبار فعلي للنظام v4.0</h1>
            <p>اختبار شامل للثغرات المُختبرة تلقائياً والصور الحقيقية</p>
        </div>

        <div class="test-section">
            <h2>🎯 إعدادات الاختبار</h2>
            <div>
                <label>🌐 الموقع المستهدف:</label>
                <input type="text" id="testUrl" value="http://testphp.vulnweb.com/" style="width: 300px; padding: 8px; margin: 5px;">
            </div>
            <div>
                <button class="btn" onclick="startRealTest()" id="startBtn">🚀 بدء الاختبار الفعلي</button>
                <button class="btn" onclick="checkSystemStatus()" id="statusBtn">🔍 فحص حالة النظام</button>
                <button class="btn" onclick="clearResults()" id="clearBtn">🗑️ مسح النتائج</button>
            </div>
            <div class="progress">
                <div class="progress-bar" id="progressBar"></div>
            </div>
            <div id="currentTest" class="test-info" style="display: none;">
                <span class="loading"></span> جاري تنفيذ: <span id="currentTestName">...</span>
            </div>
        </div>

        <div class="test-section">
            <h2>📊 نتائج الاختبار الفعلي</h2>
            <div id="testResults">
                <div class="test-info">انقر على "بدء الاختبار الفعلي" لبدء الاختبار</div>
            </div>
        </div>

        <div class="test-section">
            <h2>📋 تفاصيل النظام</h2>
            <div id="systemDetails">
                <div class="test-info">انقر على "فحص حالة النظام" لعرض التفاصيل</div>
            </div>
        </div>
    </div>

    <!-- تحميل ملفات النظام v4.0 -->
    <script src="./assets/modules/bugbounty/BugBountyCore.js"></script>
    <script src="./assets/modules/bugbounty/impact_visualizer.js"></script>
    <script src="./assets/modules/bugbounty/prompt_only_system.js"></script>
    <script src="./assets/modules/bugbounty/python_screenshot_bridge.js"></script>
    <script src="./assets/modules/bugbounty/report_exporter.js"></script>

    <script>
        let testInProgress = false;
        let testResults = [];
        let currentTestIndex = 0;
        let totalTests = 6;

        async function startRealTest() {
            if (testInProgress) {
                alert('الاختبار قيد التنفيذ بالفعل');
                return;
            }

            testInProgress = true;
            testResults = [];
            currentTestIndex = 0;
            
            const startBtn = document.getElementById('startBtn');
            const testUrl = document.getElementById('testUrl').value;
            
            startBtn.disabled = true;
            startBtn.textContent = '⏳ جاري الاختبار...';
            
            updateProgress(0);
            showCurrentTest('بدء الاختبار الفعلي');
            
            try {
                // 1. فحص تحميل النظام
                await runTest('فحص تحميل النظام', testSystemLoading);
                
                // 2. فحص الفحص التلقائي
                await runTest('فحص الفحص التلقائي', () => testAutomaticScanning(testUrl));
                
                // 3. فحص اختبار الثغرات
                await runTest('فحص اختبار الثغرات تلقائياً', testVulnerabilityTesting);
                
                // 4. فحص التقاط الصور
                await runTest('فحص التقاط الصور الحقيقية', () => testScreenshotCapture(testUrl));
                
                // 5. فحص إنتاج التقارير
                await runTest('فحص إنتاج التقارير', testReportGeneration);
                
                // 6. النتيجة النهائية
                await runTest('التقييم النهائي', finalAssessment);
                
                hideCurrentTest();
                displayFinalResults();
                
            } catch (error) {
                addTestResult('خطأ في الاختبار', false, `خطأ عام: ${error.message}`);
            } finally {
                testInProgress = false;
                startBtn.disabled = false;
                startBtn.textContent = '🚀 بدء الاختبار الفعلي';
                updateProgress(100);
            }
        }

        async function runTest(testName, testFunction) {
            showCurrentTest(testName);
            currentTestIndex++;
            updateProgress((currentTestIndex / totalTests) * 100);
            
            try {
                const result = await testFunction();
                addTestResult(testName, result.success, result.details);
                await sleep(1000); // توقف قصير بين الاختبارات
            } catch (error) {
                addTestResult(testName, false, `خطأ: ${error.message}`);
            }
        }

        function testSystemLoading() {
            const coreExists = typeof window.BugBountyCore !== 'undefined';
            const visualizerExists = typeof window.ImpactVisualizer !== 'undefined';
            const promptSystemExists = typeof window.PromptOnlyBugBountySystem !== 'undefined';
            
            const allLoaded = coreExists && visualizerExists && promptSystemExists;
            
            return {
                success: allLoaded,
                details: allLoaded ? 
                    'جميع مكونات النظام v4.0 محملة بنجاح' : 
                    `مكونات مفقودة - Core: ${coreExists}, Visualizer: ${visualizerExists}, Prompt: ${promptSystemExists}`
            };
        }

        async function testAutomaticScanning(url) {
            try {
                if (typeof window.BugBountyCore === 'undefined') {
                    return { success: false, details: 'BugBountyCore غير محمل' };
                }

                const core = new window.BugBountyCore();
                
                // محاولة فحص تلقائي مبسط
                const scanResult = await core.performQuickScan(url);
                
                const success = scanResult && scanResult.vulnerabilities && scanResult.vulnerabilities.length > 0;
                
                return {
                    success: success,
                    details: success ? 
                        `تم اكتشاف ${scanResult.vulnerabilities.length} ثغرة تلقائياً` : 
                        'لم يتم اكتشاف ثغرات أو الدالة غير متاحة'
                };
                
            } catch (error) {
                return { success: false, details: `خطأ في الفحص التلقائي: ${error.message}` };
            }
        }

        function testVulnerabilityTesting() {
            // اختبار وجود دوال اختبار الثغرات
            const visualizerExists = typeof window.ImpactVisualizer !== 'undefined';
            
            if (!visualizerExists) {
                return { success: false, details: 'ImpactVisualizer غير متاح' };
            }
            
            try {
                const visualizer = new window.ImpactVisualizer();
                const hasTestingMethods = typeof visualizer.simulateSecureExploitation === 'function';
                
                return {
                    success: hasTestingMethods,
                    details: hasTestingMethods ? 
                        'دوال اختبار الثغرات تلقائياً متاحة' : 
                        'دوال اختبار الثغرات غير متاحة'
                };
                
            } catch (error) {
                return { success: false, details: `خطأ في اختبار الثغرات: ${error.message}` };
            }
        }

        async function testScreenshotCapture(url) {
            try {
                const bridgeExists = typeof window.PythonScreenshotBridge !== 'undefined';
                
                if (!bridgeExists) {
                    return { success: false, details: 'Python Screenshot Bridge غير متاح' };
                }
                
                const bridge = new window.PythonScreenshotBridge();
                const hasCaptureMethods = typeof bridge.captureVulnerabilitySequence === 'function';
                
                return {
                    success: hasCaptureMethods,
                    details: hasCaptureMethods ? 
                        'نظام التقاط الصور الحقيقية متاح' : 
                        'نظام التقاط الصور غير متاح'
                };
                
            } catch (error) {
                return { success: false, details: `خطأ في اختبار التقاط الصور: ${error.message}` };
            }
        }

        function testReportGeneration() {
            try {
                const coreExists = typeof window.BugBountyCore !== 'undefined';
                
                if (!coreExists) {
                    return { success: false, details: 'BugBountyCore غير متاح' };
                }
                
                const core = new window.BugBountyCore();
                const hasReportMethods = typeof core.generateComprehensiveReport === 'function';
                
                return {
                    success: hasReportMethods,
                    details: hasReportMethods ? 
                        'نظام إنتاج التقارير متاح' : 
                        'نظام إنتاج التقارير غير متاح'
                };
                
            } catch (error) {
                return { success: false, details: `خطأ في اختبار إنتاج التقارير: ${error.message}` };
            }
        }

        function finalAssessment() {
            const passedTests = testResults.filter(r => r.success).length;
            const totalTests = testResults.length;
            const successRate = Math.round((passedTests / totalTests) * 100);
            
            const success = successRate >= 70; // 70% نجاح مطلوب
            
            return {
                success: success,
                details: `معدل النجاح: ${successRate}% (${passedTests}/${totalTests}) - ${success ? 'النظام يعمل بشكل مقبول' : 'النظام يحتاج إصلاحات'}`
            };
        }

        function addTestResult(testName, success, details) {
            testResults.push({
                name: testName,
                success: success,
                details: details,
                timestamp: new Date().toISOString()
            });
            
            const resultsDiv = document.getElementById('testResults');
            const resultClass = success ? 'test-success' : 'test-failure';
            const icon = success ? '✅' : '❌';
            
            resultsDiv.innerHTML += `
                <div class="${resultClass}">
                    ${icon} <strong>${testName}</strong><br>
                    📝 ${details}
                </div>
            `;
            
            resultsDiv.scrollTop = resultsDiv.scrollHeight;
        }

        function displayFinalResults() {
            const passedTests = testResults.filter(r => r.success).length;
            const totalTests = testResults.length;
            const successRate = Math.round((passedTests / totalTests) * 100);
            
            const finalClass = successRate >= 70 ? 'test-success' : 'test-failure';
            const finalIcon = successRate >= 70 ? '🎉' : '⚠️';
            
            const resultsDiv = document.getElementById('testResults');
            resultsDiv.innerHTML += `
                <div class="${finalClass}" style="margin-top: 20px; font-size: 18px;">
                    ${finalIcon} <strong>النتيجة النهائية</strong><br>
                    📊 معدل النجاح: ${successRate}%<br>
                    📈 الاختبارات الناجحة: ${passedTests}/${totalTests}<br>
                    ${successRate >= 70 ? '✅ النظام v4.0 يعمل بشكل صحيح' : '❌ النظام يحتاج إصلاحات إضافية'}
                </div>
            `;
        }

        function checkSystemStatus() {
            const systemDetails = document.getElementById('systemDetails');
            
            const status = {
                'BugBountyCore': typeof window.BugBountyCore !== 'undefined',
                'ImpactVisualizer': typeof window.ImpactVisualizer !== 'undefined',
                'PromptOnlyBugBountySystem': typeof window.PromptOnlyBugBountySystem !== 'undefined',
                'PythonScreenshotBridge': typeof window.PythonScreenshotBridge !== 'undefined',
                'ReportExporter': typeof window.ReportExporter !== 'undefined'
            };
            
            let html = '<h3>📋 حالة مكونات النظام v4.0:</h3>';
            
            for (const [component, loaded] of Object.entries(status)) {
                const icon = loaded ? '✅' : '❌';
                const statusText = loaded ? 'محمل' : 'غير محمل';
                const className = loaded ? 'test-success' : 'test-failure';
                
                html += `<div class="${className}">${icon} ${component}: ${statusText}</div>`;
            }
            
            systemDetails.innerHTML = html;
        }

        function clearResults() {
            document.getElementById('testResults').innerHTML = '<div class="test-info">تم مسح النتائج</div>';
            document.getElementById('systemDetails').innerHTML = '<div class="test-info">انقر على "فحص حالة النظام" لعرض التفاصيل</div>';
            updateProgress(0);
        }

        function updateProgress(percentage) {
            document.getElementById('progressBar').style.width = percentage + '%';
        }

        function showCurrentTest(testName) {
            document.getElementById('currentTest').style.display = 'block';
            document.getElementById('currentTestName').textContent = testName;
        }

        function hideCurrentTest() {
            document.getElementById('currentTest').style.display = 'none';
        }

        function sleep(ms) {
            return new Promise(resolve => setTimeout(resolve, ms));
        }

        // فحص حالة النظام عند التحميل
        window.addEventListener('load', () => {
            setTimeout(checkSystemStatus, 1000);
        });
    </script>
</body>
</html>
