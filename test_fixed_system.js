// اختبار سريع للنظام المُصحح
const path = require('path');
const fs = require('fs');

console.log('🔧 اختبار النظام المُصحح...');

// فحص المجلدات الموجودة
const screenshotsDir = path.join(__dirname, 'assets', 'modules', 'bugbounty', 'screenshots');

if (fs.existsSync(screenshotsDir)) {
    const folders = fs.readdirSync(screenshotsDir);
    console.log('\n📁 المجلدات الموجودة:');
    
    folders.forEach(folder => {
        console.log(`  📂 ${folder}`);
        
        const folderPath = path.join(screenshotsDir, folder);
        if (fs.statSync(folderPath).isDirectory()) {
            const files = fs.readdirSync(folderPath);
            const imageFiles = files.filter(file => file.endsWith('.png'));
            
            console.log(`    📊 عدد الصور: ${imageFiles.length}`);
            
            // عرض أول 5 صور كمثال
            imageFiles.slice(0, 5).forEach(file => {
                console.log(`    📸 ${file}`);
            });
            
            if (imageFiles.length > 5) {
                console.log(`    ... و ${imageFiles.length - 5} صور أخرى`);
            }
        }
        console.log('');
    });
    
    // تحليل النتائج
    console.log('\n📊 تحليل النتائج:');
    
    const correctFolders = folders.filter(folder => !folder.startsWith('report_'));
    const wrongFolders = folders.filter(folder => folder.startsWith('report_'));
    
    console.log(`✅ مجلدات بأسماء صحيحة: ${correctFolders.length}`);
    correctFolders.forEach(folder => console.log(`  ✅ ${folder}`));
    
    console.log(`❌ مجلدات بأسماء خاطئة: ${wrongFolders.length}`);
    wrongFolders.forEach(folder => console.log(`  ❌ ${folder}`));
    
    // فحص أسماء الصور في المجلدات الصحيحة
    if (correctFolders.length > 0) {
        console.log('\n🔍 فحص أسماء الصور في المجلدات الصحيحة:');
        
        correctFolders.forEach(folder => {
            const folderPath = path.join(screenshotsDir, folder);
            const files = fs.readdirSync(folderPath);
            const imageFiles = files.filter(file => file.endsWith('.png'));
            
            console.log(`\n📂 ${folder}:`);
            
            const correctImages = imageFiles.filter(file => 
                file.includes('_testphp_vulnweb_com.png') || 
                file.includes('before_') || 
                file.includes('during_') || 
                file.includes('after_')
            );
            
            const wrongImages = imageFiles.filter(file => 
                !file.includes('_testphp_vulnweb_com.png') && 
                !file.includes('before_') && 
                !file.includes('during_') && 
                !file.includes('after_')
            );
            
            console.log(`  ✅ صور بأسماء صحيحة: ${correctImages.length}`);
            correctImages.slice(0, 3).forEach(file => console.log(`    ✅ ${file}`));
            
            console.log(`  ❌ صور بأسماء خاطئة: ${wrongImages.length}`);
            wrongImages.slice(0, 3).forEach(file => console.log(`    ❌ ${file}`));
        });
    }
    
    // الخلاصة
    console.log('\n🎯 الخلاصة:');
    if (correctFolders.length > 0) {
        console.log('✅ تم العثور على مجلدات بأسماء صحيحة - الإصلاحات تعمل جزئياً');
    } else {
        console.log('❌ لا توجد مجلدات بأسماء صحيحة - الإصلاحات لم تطبق بعد');
    }
    
    if (wrongFolders.length > 0) {
        console.log('⚠️ لا تزال هناك مجلدات بأسماء خاطئة - يحتاج المزيد من الإصلاحات');
    }
    
} else {
    console.log('❌ مجلد الصور غير موجود');
}

console.log('\n✅ انتهى الاختبار');
